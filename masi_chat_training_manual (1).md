# **<PERSON><PERSON><PERSON> Sales Agent Training Manual**
## **First Month Warm Leads Focus - The Foundation Builder's Guide**

**Version 1.0 | Your First 30 Days at Masi Chat**

---

## **🎯 Welcome to Your Mission: Building the Foundation**

### **Why This First Month is Critical**

You're not just a sales agent—you're a **Foundation Builder**. This is Month 1 of <PERSON><PERSON> Chat's operations. We have ZERO clients, ZERO users, and ZERO real-world feedback. Your job is to change that by leveraging your most valuable asset: **the people who already know and trust you**.

### **Your Dual Mission**

1. **Intelligence Gatherer**: Collect feedback on every aspect of our business—website, app, pricing, messaging, user experience
2. **Revenue Pioneer**: Secure 10-20 founding customers from your warm network to provide initial cash flow

---

## **📋 Section 1: Understanding Your Warm Lead Universe**

### **What Makes a Lead "Warm" for You**

A warm lead is someone who:
- Already knows you personally or professionally
- Has worked with you before in any capacity
- Trusts your judgment and recommendations
- Would take a 15-minute call with you without hesitation

### **Your Warm Lead Categories (Prioritized by Conversion Potential)**

#### **🔥 Tier 1: Professional Trust Network (Highest Priority)**
- **Former bosses/managers** - They know your work ethic
- **Previous clients** from any job/industry - They've paid you before
- **Ex-colleagues** who've started their own businesses
- **Former business partners** - Shared business experience
- **Professional mentors** - They want to see you succeed

**Approach Script**: *"Hi [Name], I've joined an exciting new AI chatbot company called Masi Chat. Since I know you run [Business Name], I'd love to get your expert opinion on what we're building. Could I show you a quick demo and get your feedback? Your insights would be incredibly valuable."*

#### **🔥 Tier 2: Personal Business Network**
- **Family members with businesses** - Built-in trust
- **Friends running side hustles** - Lower pressure environment
- **Neighbors with home businesses** - Geographic proximity advantage
- **Service providers you use** (hairdresser, mechanic, doctor, etc.) - Existing relationship

**Approach Script**: *"Hey [Name], you know I've always admired what you've built with [Business]. I'm now working with a company that helps businesses like yours handle customer messages automatically. Would you be interested in seeing how it works? I'd love to get your thoughts on it."*

#### **🔥 Tier 3: Extended Network**
- **Church/community group members** with businesses
- **Parents from kids' school** who own businesses
- **Gym/sports club contacts** 
- **Friends of friends** (ask for warm introductions)

**Approach Script**: *"Hi [Name], [Mutual Friend] mentioned you run [Business Type]. I'm working with Masi Chat, and we're looking for a few business owners to try our AI customer service platform for free. Would you be open to a quick call to see if it might help with your customer inquiries?"*

---

## **📞 Section 2: The Warm Lead Outreach Process**

### **Pre-Contact Research Checklist**

Before reaching out to ANY warm lead, complete this 5-minute research:

1. **Business Check**: What type of business do they run? (Visit their website/social media)
2. **Pain Point Hypothesis**: What customer service challenges might they face?
3. **Relationship Reminder**: How do you know them? When did you last speak?
4. **Personal Touch**: Any recent life/business events you can reference?
5. **Best Contact Method**: Phone, WhatsApp, email, or in-person?

### **The 3-Touch Outreach Sequence**

#### **Touch 1: The Warm Reconnection (Day 1)**

**Channel**: Start with the most personal channel (usually phone or WhatsApp)

**Script Template**:
```
"Hi [Name]! It's [Your Name] - we [worked together at X / met through Y / you helped me with Z]. Hope you're doing well! 

I've joined an exciting new company called Masi Chat that helps businesses automate their customer service with AI. Since I know you run [Business Name], I'd love to get your expert opinion on what we're building.

Would you be open to a quick 15-minute call where I can show you how it works and get your feedback? Your insights would be incredibly valuable to us as we're just starting out.

When would be a good time for you this week?"
```

#### **Touch 2: The Value-Add Follow-Up (Day 3-4)**

**Channel**: Different channel than Touch 1 (if you called, now text/WhatsApp)

**Script Template**:
```
"Hi [Name], following up on my message about Masi Chat. I know you're busy, so I'll keep this brief.

I attached a quick 2-minute video showing how our AI chatbot works - it's actually pretty impressive what it can do for businesses like yours.

Even if you're not interested in using it, I'd really value your feedback on whether we're on the right track. 

Could we chat for 15 minutes this week? I promise it'll be worth your time."
```

#### **Touch 3: The Soft Retreat (Day 7)**

**Channel**: Email (most professional for final touch)

**Script Template**:
```
Subject: One last try - would love your feedback

Hi [Name],

I know you're swamped, so I'll make this my final ask.

We're launching Masi Chat next month, and I'm gathering feedback from successful business owners like yourself. Even if you're not interested in the product, your opinion on our approach would be incredibly helpful.

If you're not interested, no worries at all - I completely understand. But if you have 15 minutes to spare, I'd really appreciate your insights.

Thanks either way, and hope to catch up soon!

Best,
[Your Name]
```

---

## **🎭 Section 3: The Feedback-First Demo Process**

### **The Demo Mindset Shift**

**Traditional Demo Mindset**: "I'm here to sell you something"
**Your Mindset**: "I'm here to learn from you and improve our product"

This shift is CRUCIAL. You're not desperate to sell - you're genuinely seeking their expertise.

### **The 20-Minute Demo Structure**

#### **Minutes 1-3: Reconnection & Context Setting**
- Catch up personally
- Explain why you reached out to them specifically
- Frame the call as feedback gathering, not selling

**Script**: *"Thanks for taking the time, [Name]. As I mentioned, we're in the very early stages with Masi Chat, and I specifically wanted your perspective because of your experience with [their business type]. Before I show you anything, tell me - what's your biggest headache when it comes to handling customer inquiries?"*

#### **Minutes 4-8: Discovery & Pain Identification**
Use the **"5 Whys" Technique**:

1. **First Why**: "What's your biggest challenge with customer service?"
2. **Second Why**: "What happens when you can't respond immediately?"
3. **Third Why**: "How do you currently handle after-hours inquiries?"
4. **Fourth Why**: "What does a missed lead cost you?"
5. **Fifth Why**: "How would solving this impact your business?"

**Critical**: Take notes visibly. Show them you're learning from them.

#### **Minutes 9-15: Live Product Demonstration (Using Demo Account)**
- **Use your prepared demo account** to show real functionality
- Show ONLY features that address their stated pain points
- Let them see actual conversations, AI responses, and results
- Ask for feedback on every screen: "What's your first impression of this interface?"
- Gather UI/UX feedback: "Where would you expect to see [feature]?"
- Create the "wow moment" while they watch it work

#### **Minutes 16-18: The Natural Transition to Their Setup**
**Script**: *"So that's how it works for businesses like [similar example]. Now, want to see how it would look specifically for [their business name]? I can get you set up right now - it takes literally 2 minutes, and then you can start training it on your own business immediately."*

#### **Minutes 18-20: Live Account Setup Together**
- **Share your screen** showing https://app.masichat.com/account/?login
- **Guide them through the 5-field signup** while they fill it out:
  - First name, Last name, Email, Password, Repeat password
- **Help them log in immediately** after creation
- **Start basic chatbot setup** for their specific business
- **End with them having a working foundation** they can build on

### **The Two-Path Close**

Based on their response to seeing the live demo and getting their account set up, you have two paths:

#### **Path A: The Feedback Partnership** (For cautious prospects)
*"Perfect! You now have your own Masi Chat account set up and ready to go. What I'd love for you to do is spend the next couple of weeks training it with your most common customer questions and testing it with real visitors. I'll check in with you in 2 weeks to get your detailed feedback on how it performed. No cost, no commitment - just your honest opinion on what worked and what didn't. How does that sound?"*

#### **Path B: The Founding Customer Offer** (For enthusiastic prospects)
*"Based on everything you've shared and what you just saw, I think this is going to transform how you handle customer service. Since you're helping us in these early days and you can already see the value, I want to offer you our exclusive Founding Customer deal - 50% off for life. That brings our Popular Plan down to just R999/month forever. You'd be saving R12,000 per year while solving a problem that's currently costing you leads. Should I activate that founding deal for your account right now?"*

### **The Psychology Behind This Approach**

1. **Social Proof First**: They see it working before making any commitment
2. **Guided Experience**: You're there to help, reducing friction and confusion  
3. **Immediate Ownership**: They leave the call with something that belongs to them
4. **Investment Principle**: They've "worked" to set it up together, creating psychological investment
5. **Natural Next Steps**: Easy to schedule follow-up since they have something active to discuss
6. **Reduced Risk**: No commitment required until they've experienced the value

---

## **📊 Section 4: The Feedback Intelligence System**

### **What Intelligence to Gather**

During every interaction, you're collecting data in 6 categories:

#### **1. Website & Messaging Feedback**
- "When you first visited masichat.com, was it immediately clear what we do?"
- "What questions weren't answered on the homepage?"
- "How would you explain what we do to another business owner?"

#### **2. Pricing & Packaging Feedback**
- "Looking at our pricing, what's your gut reaction?"
- "Which plan seems right for a business like yours?"
- "What would make the pricing more attractive?"

#### **3. Product/UI Feedback**
- "How intuitive is the dashboard?"
- "What would you change about the interface?"
- "What features are missing that you'd expect?"

#### **4. Market Positioning Feedback**
- "How do we compare to other solutions you've seen?"
- "What makes this different from live chat tools?"
- "Who else should we be talking to?"

#### **5. Sales Process Feedback**
- "How was this demo experience for you?"
- "What would have made this conversation more valuable?"
- "What would convince you to buy vs. try for free?"

#### **6. Competitive Intelligence**
- "What are you currently using for customer service?"
- "What works/doesn't work with your current solution?"
- "What would it take to switch from what you're using now?"

### **The Weekly Intelligence Report**

Every Friday, compile your learnings into this template:

```
WEEK [X] INTELLIGENCE REPORT

Conversations This Week: [Number]
Demos Conducted: [Number]
Trials Started: [Number]
Sales Closed: [Number]

TOP 3 INSIGHTS:
1. [Insight about product/market fit]
2. [Insight about messaging/positioning]  
3. [Insight about sales process]

COMMON OBJECTIONS HEARD:
- [Objection 1] - Frequency: [X times]
- [Objection 2] - Frequency: [X times]

FEATURE REQUESTS:
- [Request 1] - Requested by: [Names]
- [Request 2] - Requested by: [Names]

WEBSITE/MESSAGING ISSUES:
- [Issue 1] - Impact: [Description]
- [Issue 2] - Impact: [Description]

COMPETITIVE MENTIONS:
- [Competitor] mentioned [X] times
- Key differentiators prospects care about: [List]

RECOMMENDED ACTIONS:
1. [Action for product team]
2. [Action for marketing team]
3. [Action for sales team]
```

---

## **🎯 Section 5: Your 30-Day Game Plan**

### **Week 1: Foundation & List Building (Target: 0 Sales, 5 Active Accounts)**

**Daily Tasks:**
- **Day 1**: Complete training, set up CRM access, study all materials
- **Day 2**: Build your warm leads list (target: 50 contacts minimum)
- **Day 3**: Start outreach sequence (Touch 1 to first 15 contacts)
- **Day 4**: Practice demo with manager, refine scripts, prepare demo account
- **Day 5**: Continue outreach, book first 3 demos for next week

**Success Metrics:**
- 50+ warm leads identified and categorized
- 15+ Touch 1 outreach messages sent
- 3+ demos booked for Week 2
- Complete understanding of product features
- Demo account prepared with sample conversations

### **Week 2: Conversation Starter (Target: 2 Sales, 8 Active Accounts)**

**Daily Tasks:**
- **Day 6-7**: Conduct first demos with live account setups
- **Day 8-9**: Continue outreach (Touch 2 follow-ups), book more demos
- **Day 10**: Compile first intelligence report, refine approach
- **Day 11-12**: More demos with account setups, start closing conversations

**Success Metrics:**
- 10+ demos conducted with live account creation
- First intelligence report submitted
- 2+ paid customers acquired
- 8+ active accounts created and being tested

### **Week 3: Momentum Building (Target: 5 Sales, 12 Active Accounts)**

**Daily Tasks:**
- **Day 13-15**: Heavy demo schedule with live setups, refined pitch delivery
- **Day 16-17**: Follow up with all account holders on their testing experience
- **Day 18-19**: Touch 3 outreach to unresponsive leads

**Success Metrics:**
- 3+ additional sales closed
- 4+ additional accounts created and active
- Clear understanding of ideal customer profile
- Objection handling mastery
- Feedback from users actively testing the platform

### **Week 4: Final Push (Target: 8 Sales, 15 Active Accounts)**

**Daily Tasks:**
- **Day 20-22**: Focus on converting active testers to paid plans
- **Day 23-24**: Last chance outreach to entire warm list
- **Day 25-26**: Final conversion push with account holders

**Success Metrics:**
- 3+ additional sales (total: 8)
- 3+ additional active accounts (total: 15)
- Complete feedback analysis from all active users
- Warm lead list fully worked
- Conversion data from free accounts to paid plans

---

## **📚 Section 6: Objection Handling Arsenal**

### **The A.R.R. Method: Acknowledge, Reframe, Respond**

#### **Objection: "We don't have budget right now"**

**Acknowledge**: "I completely understand - budget is always a concern, especially for new technology."

**Reframe**: "Here's something to consider though - what's the cost of the leads you're losing after hours? If you're missing even 2-3 qualified leads per month..."

**Respond**: "...this platform pays for itself. Plus, with our Founding Customer deal, you're locking in 50% savings forever. And if budget is tight, let's start with the 30-day free trial so you can see the ROI before spending anything."

#### **Objection: "We're too small for this"**

**Acknowledge**: "I hear that a lot from smaller businesses."

**Reframe**: "But actually, smaller businesses benefit the most from automation because every lead matters more. You can't afford to miss any."

**Respond**: "Our Starter plan is designed exactly for businesses your size - just R499/month. That's less than R17 per day to never miss a customer inquiry again."

#### **Objection: "We already have [competitor solution]"**

**Acknowledge**: "That's great - it shows you already understand the value of this type of technology."

**Reframe**: "The question is whether your current solution handles the after-hours automation and AI responses that prospects expect today."

**Respond**: "Most traditional chat tools only work when you're online. Our AI works 24/7. Would you be open to a side-by-side comparison during your free trial?"

#### **Objection: "I need to think about it"**

**Acknowledge**: "Of course - this is an important decision for your business."

**Reframe**: "Just so I can help you think through the right things, what part is causing the most hesitation - the features, the pricing, or the timing?"

**Respond**: [Address their specific concern] *"I completely understand. The good news is you already have your account set up from our call, so you can actually try it out while you're thinking it over. Test it with a few real customers, see how it handles your specific questions, and that'll give you the real data you need to make the decision. I'll check back with you in a week to see how it's going. Sound fair?"*

---

## **🏆 Section 7: Success Metrics & Tracking**

### **Daily Scorecard**

Track these metrics every day:

- **Outreach**: New contacts made
- **Conversations**: Meaningful discussions started  
- **Demos**: Product demonstrations given with live account setup
- **Accounts Created**: New accounts set up during demos
- **Feedback**: Intelligence items collected
- **Active Users**: Accounts actively testing the platform
- **Sales**: Free accounts converted to paid customers

### **Weekly Performance Review**

Every Friday, rate yourself 1-10 on:

- **Product Knowledge**: How well do I understand our solution?
- **Demo Quality**: How engaging and tailored are my demos?
- **Feedback Collection**: How much intelligence am I gathering?
- **Relationship Building**: How strong are my prospect relationships?
- **Closing Skills**: How effective am I at converting interest to action?

### **Month-End Success Definition**

You've succeeded if you achieve:

- **Minimum**: 5 paid customers + 10 active testing accounts
- **Target**: 8 paid customers + 15 active testing accounts  
- **Stretch**: 12 paid customers + 20 active testing accounts

**Plus**:
- Comprehensive intelligence report
- Documented feedback from 50+ prospects
- User experience feedback from all active accounts
- Account setup and onboarding process optimization
- Conversion rate data from free accounts to paid plans
- Refined sales process recommendations
- Identified ideal customer profile

---

## **📋 Required Supporting Documents**

Based on this manual, we need to develop:

### **1. Masi Chat Product Knowledge Guide**
- Feature-by-feature breakdown with screenshots
- Use case scenarios for each customer type
- Technical specifications and limitations
- Integration capabilities and setup requirements

### **2. Demo Script Library**
- Industry-specific demo flows
- Screen-by-screen walkthrough with talking points
- Question prompts for each feature
- Transition phrases and timing guides

### **3. Competitive Battlecards**
- Key competitors and their weaknesses
- Win/loss reasons against each competitor
- Pricing comparison charts
- Differentiation talking points

### **4. Objection Handling Playbook**
- Complete list of objections with A.R.R. responses
- Success stories for each objection type
- Escalation procedures for complex objections
- Role-play scenarios and practice exercises

### **5. CRM Training Manual**
- Step-by-step setup instructions
- Lead scoring and qualification criteria
- Pipeline management best practices
- Reporting and analytics guide

### **6. Customer Success Stories Template**
- Case study format for new wins
- Before/after metrics to highlight
- Customer quote collection guidelines
- Visual presentation templates

This manual provides the foundation for building Masi Chat's customer base while gathering critical market intelligence. Success depends on treating every warm lead conversation as a learning opportunity, not just a sales opportunity.