# **<PERSON><PERSON>t Product Knowledge Guide**
*For Sales Agents*

---

## **Table of Contents**
1. [Platform Overview](#platform-overview)
2. [Core Features by Customer Type](#core-features-by-customer-type)
3. [Feature-by-Feature Breakdown](#feature-by-feature-breakdown)
4. [Use Case Scenarios](#use-case-scenarios)
5. [Technical Specifications & Limitations](#technical-specifications--limitations)
6. [Integration Capabilities](#integration-capabilities)
7. [Pricing & Plans](#pricing--plans)
8. [Common Objections & Responses](#common-objections--responses)

---

## **Platform Overview**

Masi Chat is a comprehensive omnichannel customer communication platform that combines AI-powered chatbots, live chat, and marketing automation. It's designed to help businesses automate customer support, generate leads, and provide 24/7 customer service across multiple channels.

### **Key Value Propositions:**
- **No-Code Setup**: Automated training from website content
- **24/7 Operation**: Continuous availability with human backup
- **Omnichannel**: Web, WhatsApp, Telegram, SMS, email, social media
- **Dual AI Platform**: OpenAI + Dialogflow integration
- **Scalable**: From 10K to 1M+ messages per month
- **Cost-Effective**: 50% launch discount, all features included

---

## **Core Features by Customer Type**

### **1. Support-Focused Businesses (Customer Service Teams)**
*Primary Goal: Reduce support volume and improve response times*

#### **Essential Features:**
- **Conversation Management**: Centralized dashboard with search, delete, download capabilities
- **Real-Time Indicators**: Online/typing status, queue position, waiting times
- **Knowledge Base**: Multilingual help center with searchable widget
- **Quick Saved Replies**: Pre-written responses with keyboard shortcuts
- **Department Routing**: Intelligent assignment with email notifications
- **Comprehensive Notifications**: Email, push, desktop, SMS, flash notifications
- **Agent Assignment**: Fair distribution or user-selected preferred agents
- **Email Integration**: IMAP/POP3 integration, reply via email
- **Reports & Analytics**: Response times, agent ratings, performance metrics

#### **ROI Metrics:**
- Reduce response time by 70%
- Handle 3x more conversations per agent
- 24/7 availability without additional staffing costs

### **2. Marketing-Driven Businesses (Lead Generation & Conversion)**
*Primary Goal: Capture leads and increase conversion rates*

#### **Essential Features:**
- **Pop-up Messages**: Multiple automated, multilingual campaigns
- **Welcome & Follow-up**: Automated greetings and lead capture
- **Subscribe Integration**: Newsletter signup with major services
- **Automations & Triggers**: 20+ conditions with 5 automation types
- **Bulk Messaging**: Multi-user direct messages with merge fields
- **Proactive Chat**: Agent-initiated conversations with visitors
- **Rich Messages & Surveys**: 16 message types with custom shortcodes
- **Social Sharing**: Facebook, Twitter, WhatsApp, LinkedIn integration
- **Booking System**: Calendly integration for appointments

#### **ROI Metrics:**
- Increase lead capture by 40%
- Improve conversion rates by 25%
- Reduce cost per lead by 60%

### **3. AI-First Businesses (Automation & Efficiency)**
*Primary Goal: Maximize automation while maintaining quality*

#### **Essential Features:**
- **Dual AI Platform**: OpenAI + Dialogflow for maximum capability
- **Automated Training**: Website content auto-training + real-time updates
- **Multilingual AI**: 50+ languages with real-time translation
- **Human Takeover**: Seamless agent intervention
- **Smart Features**: Reply suggestions, message rewriting, spell check
- **Multiple AI Assistants**: Unlimited OpenAI assistants for different areas
- **Custom Chatbot Flows**: Sequence interactions with data collection
- **Voice Recognition**: OpenAI transcription for audio messages
- **Analytics**: Performance tracking, sentiment analysis
- **Ready Templates**: 45+ pre-built Dialogflow templates

#### **ROI Metrics:**
- Automate 80% of routine inquiries
- Reduce operational costs by 50%
- Scale support without proportional staff increase

---

## **Feature-by-Feature Breakdown**

### **Communication Features**

#### **Live Chat**
- **Real-time messaging** with typing indicators
- **File sharing** (images, documents, videos)
- **Rich messages** (16 types: card, slider, slider-images, chips, buttons, select, inputs, email, timetable, articles, list, list-double, list-image, table, registration, share)
- **Message history** with search and export
- **Conversation notes** and internal comments

#### **Omnichannel Integration**
- **WhatsApp Business API** (Cloud API & Twilio)
- **Facebook Messenger** with automatic replies
- **Telegram** with bot integration
- **SMS/Text messaging** with phone number management
- **Email integration** (IMAP/POP3)
- **Twitter/X** direct messages
- **Viber, LINE, WeChat, Zalo** support

### **AI & Automation Features**

#### **OpenAI Integration**
- **GPT-powered responses** with context awareness
- **Automatic training** from website content
- **Voice transcription** for audio messages
- **Audio transcription** with OpenAI Whisper
- **Multiple assistants** for different use cases
- **Smart reply suggestions**
- **Message rewriting** and spell correction

#### **Dialogflow Integration**
- **Natural language processing**
- **Intent recognition** and entity extraction
- **45+ ready-made templates**
- **Custom conversation flows**
- **Knowledge base integration**
- **Multilingual support**

### **Marketing & Lead Generation**

#### **Pop-ups & Campaigns**
- **Automated pop-ups** via automation conditions
- **Custom design** and branding
- **Multilingual campaigns**

#### **Automation Triggers**
- **20+ trigger conditions**:
  - Date time
  - Repeat
  - Browsing time
  - Scroll position
  - Current URL
  - Referring URL
  - User type
  - Returning visitor
  - Country
  - Language
  - City
  - Website
  - Birthdate
  - Company
  - Postal code
  - Email
  - Phone
  - Creation time
  - Custom variable

- **5 automation types**:
  - Messages
  - Pop-ups
  - Emails
  - Design
  - More (Name, Department ID, Agent ID, Tags, Article IDs, Articles category, Conditions)

### **Management & Analytics**

#### **User Management**
- **Agent roles** (admin, agent, viewer)
- **Department organization**
- **Access restrictions**
- **Performance tracking**

#### **Reporting & Analytics**
- **Conversation metrics** (volume, response time, resolution rate)
- **Agent performance** (ratings, response time, conversations handled)
- **User management** and conversation tracking

---

## **Use Case Scenarios**
*Quick KB Navigation Guide for Sales Conversations*

### **Scenario 1: E-commerce Customer Support**
**Customer Profile**: Online retailer with high order inquiry volume
**Challenge**: "We get hundreds of 'where's my order' questions daily"

**Masi Chat Navigation Path**:
1. **Function Calling for Order Status** → Navigate to: **Chatbot > Q&A > Function calling**
   - Show: "Allow users to check order statuses, fetch information, or query external systems directly through the chatbot"
   - Example: `{"order_status": "Delivered 2 hours and 25 minutes ago"}`

2. **Saved Replies for Common Questions** → Navigate to: **Settings > Saved replies**
   - Show: Pre-written messages for order inquiries
   - Feature: Type `#` followed by saved reply name to quickly access responses

3. **WhatsApp Order Processing** → Navigate to: **WhatsApp > WhatsApp Shop**
   - Show: "Masi Chat automatically processes the order and sends confirmation message back to the customer"
   - Feature: Centralized webhook handles order processing

4. **Human Takeover** → Navigate to: **Settings > Human takeover**
   - Show: "Manual takeover automatically notify agents via email and leave conversation marked as unread"

**Demo Flow**: Customer asks "Where's my order #12345?" → Function Calling pulls from e-commerce API → Returns status → Sends WhatsApp confirmation

### **Scenario 2: SaaS Lead Generation**
**Customer Profile**: B2B software needing qualified leads
**Challenge**: "Our website visitors don't convert to demos"

**KB Navigation Path**:
1. **Pop-up Campaigns** → Navigate to: **Settings > Automations**
   - Show: 20+ trigger conditions including "Browsing time", "Scroll position", "Current URL", "Returning visitor"
   - Feature: 5 automation types (messages, pop-ups, emails, design, more)

2. **Lead Qualification** → Navigate to: **Users > Manage users**
   - Show: User types - "lead" is automatically registered user with at least one conversation
   - Feature: Automatic lead creation and classification

3. **Calendly Integration** → Navigate to: **Settings > Calendly**
   - Show: Button rich message with Calendly URL
   - Example: `[button link="https://calendly.com/company/demo" name="Schedule Demo" success="Thank you! Your demo has been scheduled."]`

4. **Follow-up Automation** → Navigate to: **Settings > Messages & Forms > Built-in messages**
   - Show: "Send a message to new users when they visit the website for the first time"
   - Feature: Automated follow-up when agents unavailable

**Demo Flow**: Visitor on pricing page → Exit-intent pop-up → AI qualifies budget/timeline → Calendly booking → Automated email follow-up

### **Scenario 3: Healthcare Appointment Booking**
**Customer Profile**: Medical practice with multiple locations
**Challenge**: "Phone lines are always busy with appointment requests"

**Masi Chat Navigation Path**:
1. **Department Routing** → Navigate to: **Settings > Departments**
   - Show: "Intelligent routing with email notifications and access restrictions"
   - Feature: Route patients to correct location/specialist

2. **Queue Management** → Navigate to: **Settings > Queue and routing**
   - Show: "Live waiting time/position updates with concurrent chat limits"
   - Feature: Real-time queue position for patients

3. **SMS Notifications** → Navigate to: **Notifications > Text message notifications**
   - Show: Twilio integration for appointment confirmations
   - Feature: "All phone numbers must have country code starting with +"

4. **Multilingual Support** → Navigate to: **Settings > Language and translations**
   - Show: "Masi Chat is fully multilingual and provides powerful features to detect user's language on the fly"
   - Feature: Automatic language detection for diverse patient base

**Demo Flow**: Patient asks "Book appointment" → AI detects language → Collects symptoms → Routes to appropriate department → Confirms via SMS with queue position

### **Scenario 4: Real Estate Lead Capture**
**Customer Profile**: Real estate agency with property listings
**Challenge**: "Visitors browse properties but don't leave contact info"

**Masi Chat Navigation Path**:
1. **Proactive Engagement** → Navigate to: **Settings > Automations**
   - Show: Trigger conditions like "Current URL", "Browsing time", and "Scroll position"
   - Feature: Agents can initiate conversations with online visitors

2. **Rich Property Messages** → Navigate to: **Conversations > Rich messages**
   - Show: 16 message types including card, slider, slider-images, chips, buttons
   - Feature: Property cards with images, descriptions, and contact buttons using [card] shortcode

3. **Lead Auto-Registration** → Navigate to: **Users > User types**
   - Show: "A 'lead' is any user with no user details, who is automatically registered, and with at least one conversation"
   - Feature: Automatic lead creation when visitors engage

4. **Agent Territory Assignment** → Navigate to: **Settings > Queue and routing**
   - Show: "Fair distribution or user-selected preferred agents"
   - Feature: Route leads to agents based on property location

**Demo Flow**: Visitor views property listing → Time-based automation triggers → Rich message with property details → Lead captured → Assigned to local agent

---

## **Masi Chat Navigation Quick Reference for Common Questions**

### **"How does the AI learn about our business?"**
**Masi Chat Navigation**: **Chatbot > Training**
**Key Point**: "Automatic training from website content + real-time Q&A additions"
**Show**: Website content auto-training feature

### **"What if the AI can't answer something?"**
**Masi Chat Navigation**: **Settings > Human takeover**
**Key Point**: "Seamless agent intervention when AI can't respond"
**Show**: "Manual takeover automatically notify agents via email"

### **"Can it work with WhatsApp?"**
**Masi Chat Navigation**: **Settings > Apps > WhatsApp** (Cloud API or Twilio)
**Key Point**: "Full WhatsApp Business API integration with order processing"
**Show**: "Masi Chat automatically processes the order and sends confirmation message"

### **"How do we track performance?"**
**Masi Chat Navigation**: **Reports** (main navigation menu)
**Key Point**: "Daily conversations, response times, agent ratings, comprehensive performance metrics"
**Show**: Analytics dashboard with conversation metrics

### **"What about different languages?"**
**KB Navigation**: **Settings > Language and translations**
**Key Point**: "Masi Chat is fully multilingual with powerful features to detect user's language on the fly"
**Show**: 50+ languages with automatic detection

### **"Can it integrate with our CRM?"**
**Masi Chat Navigation**: **Settings > Apps** (for pre-built) or **Chatbot > Q&A > Function calling** (for custom)
**Key Point**: "Connect to external tools and systems by fetching real-time data through API integrations"
**Show**: Zendesk, HubSpot integrations + custom API capabilities

### **"How do we handle common questions quickly?"**
**Masi Chat Navigation**: **Settings > Saved replies**
**Key Point**: "Pre-written messages that agents can quickly access by typing # followed by the saved reply name"
**Show**: Canned responses with keyboard shortcuts

---

## **Technical Specifications & Limitations**

### **System Requirements**
- **Browser Support**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Compatibility**: iOS 12+, Android 8+
- **Internet Connection**: Stable broadband recommended
- **No software installation** required (cloud-based)

### **Performance Specifications**
- **Message Processing**: Up to 1M+ messages per month
- **Response Time**: < 100ms for AI responses
- **Uptime**: 99.9% guaranteed
- **Concurrent Users**: Unlimited
- **File Upload**: Up to 25MB per file
- **Supported Formats**: Images, PDFs, documents, videos

### **Security & Compliance**
- **Data Encryption**: SSL/TLS encryption in transit
- **Data Storage**: Secure cloud infrastructure
- **GDPR Compliant**: EU data protection standards
- **Privacy Controls**: User data deletion options
- **Access Controls**: Role-based permissions

### **API & Integration Limits**
- **API Rate Limits**: 1000 requests per minute
- **Webhook Support**: Real-time event notifications
- **Custom Integrations**: REST API available
- **Third-party Apps**: 20+ pre-built integrations

### **Known Limitations**

#### **WhatsApp Limitations**
- Cannot message users after 24 hours without template
- Template approval required for marketing messages
- Media file size limits (16MB for videos, 5MB for images)

#### **AI Limitations**
- Requires quality training data for optimal performance
- May need human intervention for complex queries
- Language detection accuracy varies by language
- Context retention limited to conversation session

#### **Channel-Specific Limitations**
- **Twitter/X**: Maximum 5 messages per 24-hour window
- **Telegram**: Some rich message types not supported
- **WeChat**: Links converted to text, files not supported
- **LINE**: Stickers not supported, 7-day response window

---

## **Integration Capabilities**

### **CRM Integrations**
- **Salesforce**: Contact sync, lead creation
- **HubSpot**: Contact management, deal tracking
- **Pipedrive**: Lead qualification, opportunity creation
- **Zendesk**: Ticket creation, customer history
- **Custom CRM**: API integration available

### **Marketing Tools**
- **Mailchimp**: Newsletter subscription, segmentation
- **Constant Contact**: Email list management
- **ActiveCampaign**: Marketing automation
- **Calendly**: Appointment booking
- **Google Analytics**: Conversion tracking

### **E-commerce Platforms**
- **Shopify**: Order tracking, product recommendations
- **WooCommerce**: Cart abandonment, order updates
- **Magento**: Customer support, order management
- **Custom E-commerce**: API integration

### **Communication Channels**
- **WhatsApp Business API** (Cloud API & Twilio)
- **Facebook Messenger**
- **Telegram**
- **SMS/Text** (multiple providers)
- **Email** (IMAP/POP3)
- **Social Media** (Twitter, Instagram)

### **Setup Requirements**

#### **Basic Setup (15 minutes)**
1. Account creation and verification
2. Website widget installation (single code snippet)
3. Basic AI training from website content
4. Agent account setup

#### **Advanced Setup (1-2 hours)**
1. Channel integrations (WhatsApp, social media)
2. Custom chatbot flows
3. Department and routing configuration
4. Analytics and reporting setup
5. Third-party integrations

#### **Enterprise Setup (1-2 days)**
1. Custom API integrations
2. Advanced automation workflows
3. Multi-language configuration
4. Custom branding and white-labeling
5. Staff training and onboarding

---

## **Pricing & Plans**

### **Pricing Structure**
- **Message-based pricing**: Pay for what you use
- **All features included**: No feature restrictions
- **50% launch discount**: Available for new customers
- **No setup fees**: Quick start included

### **Typical Plan Ranges**
- **Starter**: 10K messages/month - Small businesses
- **Growth**: 50K messages/month - Growing companies
- **Professional**: 200K messages/month - Established businesses
- **Enterprise**: 1M+ messages/month - Large organizations

### **Value Proposition**
- **Cost per message**: Significantly lower than competitors
- **No per-agent fees**: Unlimited agents included
- **All channels included**: No additional channel fees
- **Free trial**: 14-day full-feature trial

---

## **Common Objections & Responses**

### **"We already have a chat solution"**
**Response**: "That's great! What specific challenges are you facing with your current solution? Masi Chat's unique dual AI platform (OpenAI + Dialogflow) and omnichannel approach often provides capabilities that single-purpose tools can't match. Would you be interested in a side-by-side comparison?"

### **"AI chatbots don't work for our complex business"**
**Response**: "I understand that concern. Masi Chat's human takeover feature ensures complex queries are seamlessly transferred to your team. Plus, our dual AI platform learns from every interaction. Many of our clients started with the same concern and now automate 70-80% of their inquiries. Would you like to see how it handles your specific use cases?"

### **"We don't have technical resources for setup"**
**Response**: "That's exactly why we built Masi Chat with no-code setup. Our AI automatically trains from your website content, and the basic setup takes just 15 minutes. We also provide full onboarding support and can handle the technical setup for you."

### **"The pricing seems too good to be true"**
**Response**: "We understand that concern. Our competitive pricing comes from our efficient cloud infrastructure and the fact that we include all features in every plan - no hidden fees or per-agent charges. We're confident in our value, which is why we offer a 14-day free trial with full access to all features."

### **"We need to integrate with our existing systems"**
**Response**: "Integration is one of our strengths. We have pre-built integrations with 20+ popular platforms including Salesforce, HubSpot, Shopify, and more. For custom systems, our REST API makes integration straightforward. Our technical team can also assist with the integration process."

### **"What about data security and compliance"**
**Response**: "Security is paramount. We use SSL/TLS encryption, maintain 99.9% uptime, and are GDPR compliant. Your data is stored in secure cloud infrastructure with role-based access controls. We can provide detailed security documentation for your review."

### **"We need multilingual support"**
**Response**: "Perfect! Masi Chat supports 50+ languages with real-time translation. Our AI can detect the user's language automatically and respond appropriately. Many of our global clients use this feature to provide consistent support across different markets."

### **"How do we measure ROI?"**
**Response**: "Great question! Our analytics dashboard tracks key metrics like response times, resolution rates, lead conversion, and automation percentages. Most clients see ROI within 30-60 days through reduced support costs and increased lead capture. We can help you establish baseline metrics and track improvement."

---

## **Quick Reference: Key Selling Points**

### **For Support Teams**
✅ Reduce response time by 70%
✅ Handle 3x more conversations per agent
✅ 24/7 availability without additional staff
✅ Comprehensive analytics and reporting

### **For Marketing Teams**
✅ Increase lead capture by 40%
✅ Improve conversion rates by 25%
✅ Automated lead qualification
✅ Multi-channel campaign management

### **For AI-First Companies**
✅ Automate 80% of routine inquiries
✅ Dual AI platform for maximum capability
✅ Seamless human takeover
✅ Continuous learning and improvement

### **Universal Benefits**
✅ No-code setup in 15 minutes
✅ All features included in every plan
✅ 50% launch discount available
✅ 14-day free trial with full access
✅ Omnichannel support (web, WhatsApp, social media)
✅ 99.9% uptime guarantee

---

*This guide should be used in conjunction with live demos and customer-specific use case discussions. Always confirm current pricing and feature availability with the latest product documentation.*
**Response**: "I understand that concern. Masi Chat's human takeover feature ensures complex queries are seamlessly transferred to your team. Plus, our dual AI platform learns from every interaction. Many of our clients started with the same concern and now automate 70-80% of their inquiries. Would you like to see how it handles your specific use cases?"

### **"We don't have technical resources for setup"**
**Response**: "That's exactly why we built Masi Chat with no-code setup. Our AI automatically trains from your website content, and the basic setup takes just 15 minutes. We also provide full onboarding support and can handle the technical setup for you."

### **"The pricing seems too good to be true"**
**Response**: "We understand that concern. Our competitive pricing comes from our efficient cloud infrastructure and the fact that we include all features in every plan - no hidden fees or per-agent charges. We're confident in our value, which is why we offer a 14-day free trial with full access to all features."

### **"We need to integrate with our existing systems"**
**Response**: "Integration is one of our strengths. We have pre-built integrations with 20+ popular platforms including Salesforce, HubSpot, Shopify, and more. For custom systems, our REST API makes integration straightforward. Our technical team can also assist with the integration process."

### **"What about data security and compliance"**
**Response**: "Security is paramount. We use SSL/TLS encryption, maintain 99.9% uptime, and are GDPR compliant. Your data is stored in secure cloud infrastructure with role-based access controls. We can provide detailed security documentation for your review."

### **"We need multilingual support"**
**Response**: "Perfect! Masi Chat supports 50+ languages with real-time translation. Our AI can detect the user's language automatically and respond appropriately. Many of our global clients use this feature to provide consistent support across different markets."

### **"How do we measure ROI?"**
**Response**: "Great question! Our analytics dashboard tracks key metrics like response times, resolution rates, lead conversion, and automation percentages. Most clients see ROI within 30-60 days through reduced support costs and increased lead capture. We can help you establish baseline metrics and track improvement."

---

## **Quick Reference: Key Selling Points**

### **For Support Teams**
✅ Reduce response time by 70%
✅ Handle 3x more conversations per agent
✅ 24/7 availability without additional staff
✅ Comprehensive analytics and reporting

### **For Marketing Teams**
✅ Increase lead capture by 40%
✅ Improve conversion rates by 25%
✅ Automated lead qualification
✅ Multi-channel campaign management

### **For AI-First Companies**
✅ Automate 80% of routine inquiries
✅ Dual AI platform for maximum capability
✅ Seamless human takeover
✅ Continuous learning and improvement

### **Universal Benefits**
✅ No-code setup in 15 minutes
✅ All features included in every plan
✅ 50% launch discount available
✅ 14-day free trial with full access
✅ Omnichannel support (web, WhatsApp, social media)
✅ 99.9% uptime guarantee

---

*This guide should be used in conjunction with live demos and customer-specific use case discussions. Always confirm current pricing and feature availability with the latest product documentation.*
