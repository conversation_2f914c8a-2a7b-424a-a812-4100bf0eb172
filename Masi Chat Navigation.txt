

Settings 
Settings > chat
Login initialization
Initialize and display the chat widget only when the user is logged in.


Dashboard display
Display the dashboard instead of the chat area on initialization.


Disable dashboard
Disable the dashboard, and allow only one conversation per user.


Allow only one conversation
Allow only one conversation per user.


Hide chat outside of office hours
Disable and hide the chat widget outside of scheduled office hours.


Hide chat if no agents online
Disable and hide the chat widget if all agents are offline.


Language
Set the chat language or translate it automatically to match the user language. Default is English.


Default
RTL
Activate the Right-To-Left (RTL) reading layout.


Open automatically
Open the chat window automatically when a new message is received.


Disable uploads
Disable file uploading capabilities within the chat.


Disable voice messages
Disable voice message capabilities within the chat.


Close chat
Allow the user to archive a conversation and hide archived conversations.


Manual initialization
Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.


Agents menu
Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.

Active

One conversation per agent

Display online agents only

Dashboard title
Agents 
Messaging channels
Allow users to contact you via their favorite messaging apps.

Active

Dashboard title
Messaging channels
WhatsApp
https://wa.me/27781785828
Messenger
Instagram
Twitter
Telegram
Viber
Zalo
LINE
WeChat
Email
mailto:<EMAIL>
Ticket
https://chat.app.masichat.com/5836022?ticket



Settings>admin
Collapse panels
Automatically collapse the conversation details panel, and other panels, of the admin area.


Hide conversation details panel
Automatically hide the conversation details panel.


Automatically translate admin area
Automatically translate the admin area to match the agent profile language or browser language.


Do not translate settings area
Activate this option if you don't want to translate the settings area.


Automatically archive conversations
Archive automatically the conversations marked as read every 24h.


Sort conversations by date
Always sort conversations by date in the admin area.


Agent privileges
Set which actions to allow agents.

Users area

Articles area

Edit user

Agents and admins tab

Delete conversation

Delete message

Update conversation department

Supervisors
Set which actions to allow supervisors.

Admin IDs
796
Users area

Settings area

Reports area

Articles area

Edit user

Agents and admins tab

Delete conversation

Delete message

Send message

RTL
Activate the Right-To-Left (RTL) reading layout for the admin area.


Show profile images
Show the profile image of agents and users within the conversation.


Disable features
Disable any features that you don't need.

Channels filter

Filters

Attachments list

Tags

Notes

Notes settings
Manage the notes settings.

Hide username

Tags settings
Manage the tags settings.

Display in conversation list

Starred tag

Tags
Add and manage tags.

Name
Transcript
Transcript settings.

Type

CSV
Button action

Download
Message
Users table additional columns
Displays additional columns in the user table. Enter the name of the fields to add.

Name
Saved replies
Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \n to do a line break.

Name
Text
Primary color
Set the primary color of the admin area.

Secondary color
Set the secondary color of the admin area.

Switch accounts
Switch between accounts without having to sign out and in.

Name
Email
Password



Settings=notifications
Sound settings
Play a sound for new messages and conversations.

Active

Active - admin

Volume

Default
Volume - admin

Default
Repeat

2
Repeat - admin

Default
Online users notification
Show a notification and play a sound when a new user is online.


Away mode
Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.


Desktop notifications
Show a desktop notification when a new message is received.


Users and agents
Flash notifications
Show a browser tab notification when a new message is received.


Users and agents
Push notifications
Push notifications are provided by Masi Chat. Enable or disable them below.

Active for agents

Active for users

Subscribe
Agent email notifications
Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.


User email notifications
Send an email to the user when an agent replies and the user is offline.


Do not send email notifications to admins
Prevent admins from receiving email notifications.


User notification email
Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.

Subject
New Reply from {sender_name} – Masi Chat Support  
Content
**Hi {recipient_name},**  

{sender_name} has responded to your inquiry:  
  
 "{message}"
  
👉 View the full conversation at {conversation_url_parameter}
  
If you need further assistance, simply reply to this email.  
  
**Best regards,**  
The Masi Chat Team
Languages
Agent notification email
Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.

Subject
 New Message from {sender_name} – Masi Chat
Content
 **Hi {recipient_name},**  

You have a new message from {sender_name}: 

 "{message}"

👉 Open Conversation {conversation_link}

Please respond as soon as possible to ensure a great customer experience. 

**Best,** 
 The Masi Chat Team
Languages
Send a user email notification
Send an email notification to the provided email address.

Send an agent email notification
Send an email notification to the provided email address.

SMTP
Outgoing SMTP server information. The default SMTP server is provided by MasiChat, but you can configure your own if needed.

Host
mail.masichat.com
Username
<EMAIL>
Password
••••••••
Port
465
Sender email
<EMAIL>
Sender name
Masi Chat Support
Troubleshoot
Email piping
The email address of Settings > Notifications > SMTP > Sender email must match the one used by the email piping server.

Active

Host
mail.masichat.com
Username
<EMAIL>
Password
••••••••
Port

993
Delimiter

Convert all emails

Department ID
Synchronize emails
Email signature
Set the default email signature that will be appended to automated emails and direct emails.

Languages
Email header
Set the default email header that will be prepended to automated emails and direct emails.

Languages
Text message notifications
Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.

Active for agents

Active for users

Account SID
Token
Sender number
Agent message template
User message template
Configuration URL
Send a user text message notification
Send a text message to the provided phone number.

Send an agent text message notification
Send a text message to the provided phone number.

Notifications icon
Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.



Settings=users
Require registration
Require the user registration or login before start a chat. To enable the login area the password field must be included.


Disabled
Disable registration during office hours
Enable the registration outside of scheduled office hours only.


Disable registration if agents online
Enable the registration only if all agents are offline.


Registration form
User registration form information.

Form title
Registration form
Form message
Hi, please register before asking a question 
Success message
Thanks 
Button text
Submit 
Terms link
https://masichat.com/terms-of-service/
Privacy link
https://masichat.com/privacy-policy/
Login form
User login form information.

Form title
Login Form
Form message
Please fill in the form to login in order to get your queries resolved. Thanks 
Registration fields
Choose which fields to include in the registration form. The name field is included by default.

Email

Required

Phone

Required

Last name

Required

Profile image

Required

City

Required

Country

Required

Language

Required

Birthday

Required

Company

Required

Website

Required

Custom fields
Add custom fields to the user profile details.

Name
Required

Include custom fields
Include custom fields in the registration form.


User details in success message
Append the registration user details to the success message.


Email verification
Enable email verification with OTP.


Email verification email
Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.

Subject
Content
Languages
Register all visitors
Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.


Single phone country code
Force users to use only one phone country code.


Visitor name prefix
Rename the visitor name prefix. Default is 'User'.

Visitor default name
Set the default username to use in bot messages and emails when the user doesn't have a name.

Full visitor details
Save useful information like user country and language also for visitors.


Display user's profile image
Display the user's profile image within the chat.


Hide agent's profile image
Hide the agent's profile image within the chat.


Show sender's name
Show the sender's name in every message.


Chat and admin
Bot name
Rename the chat bot. Default is 'Bot'.

Masi
Bot profile image
Set a profile image for the chat bot.

Delete leads
Delete all leads and all messages and conversations linked to them.

Import users
Import users from a CSV file. You can download an example CSV file from the documentation.


Settings=design
Primary color
Set the primary color of the chat widget.

rgb(234, 115, 81)
Secondary color
Set the secondary color of the chat widget.

rgb(224, 50, 116)
Tertiary color
Set the tertiary color of the chat widget.

Chat position
Set the position of the chat widget.


Right
Header title
Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.

Header message
Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.

Header type
Set the header appearance.


Brand
Display user name in header
Replace the header title with the user's first name and last name when available.


Header brand image
Display a brand image in the header area. This only applies for the 'brand' header type.

Header background image
Apply a custom background image for the header area.

Chat button icon
Change the chat button image with a custom one.

Built-in chat button icons
Change the chat button image with a custom one.

Chat background
Choose a background texture for the chat header and conversation area.

Chat button offset
Adjust the chat button position. Values are in px. Use simple values for best results.

Top
Bottom
80
Right
Left
Apply to

All


Settings=Messages & Forms
Welcome message
Send a message to new users when they visit the website for the first time.

Active

Open chat

Sound

Disable outside of office hours

Trigger

On page load
Delay (ms)
Message
Hi there! 👋 Welcome to Masi Chat. Let us know how we can assist you today!
Follow up message
If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.

Active

Disable outside of office hours

Disable for messaging channels

Name

Last name

Phone

Phone required

Title
Delay (ms)
Sound

Message
Sorry for the wait! Please leave your email, and we’ll get back to you as soon as possible. 📧
Success message
Thanks, your message is received we’ll get back to you as soon as possible.
Placeholder text
Follow up email
Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.

Subject
Thanks for Reaching Out! 
Content
Hi {user_name},

Thanks for contacting us! We've received your message and will get back to you soon.

If you have any urgent questions, feel free to reach out to our support team.

Best regards,  
The Masi Chat Team  
Languages
Rating
Display the feedback form to rate the conversation when it is archived.

Active

Message area

Offline message
Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.

Active

Hide timetable

Disable agents check

Message type

Chat message
Title
Live agents currently offline
Message
We’re currently offline, but leave us a message, and we’ll respond as soon as we’re back online!
Close message
Send a message to the user when the agent archive the conversation.

Active

Send transcript to user's email

Message
This conversation has been closed. If you need further assistance, feel free to start a new chat
Popup message
Show a pop-up notification to all users.

Active

Hide on mobile

Profile image
Title
Do you need help?
Message
Kindly submit your request here, and our chatbot or human agent will promptly provide assistance.
Privacy message
Before initiating the chat, the user must accept a privacy message in order to gain access.

Active

Disable for messaging channels

Title
Message
Declined message
Optional link
Link name
Accept button text
Decline button text




Settings> Miscellaneous
Webhooks
Webhooks are information sent in background to a unique URL defined by you when something happens.

Active

URL
Secret key
Active webhooks
Newsletter
Subscribe users to your preferred newsletter service when they provide an email.

Active

Service

Mailchimp
List ID
Key
Scheduled office hours
Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.

Monday
To
And
To
Tuesday
To
And
To
Wednesday
To
And
To
Thursday
To
And
To
Friday
To
And
To
Saturday
To
And
To
Sunday
To
And
To
UTC offset
Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.

Departments settings
Manage here the departments settings.

Display in dashboard

Display images

Display in conversation list

One conversation per department

Label
Single label
Dashboard title
Departments
Add and manage additional support departments.

Name
Customer Support
1
Name
Technical Support
2
Name
Sales & Business Development
3
Name
Billing & Accounts Support
4
Name
Other
5
Queue
Distribute conversations proportionately between agents and notify visitors of their position within the queue.

Active

Concurrent chats
Response time
Sound

Message
Your turn message
Routing
Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.

Active

Disable online status check

Hide conversations of other agents
Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.

Active

Agents menu

Routing if offline

View unassigned conversations

Rich messages
Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.

Name
Content
Performance optimization
Disable features you don't use and improve the chat performance.

Reports

Articles

IP banning
Restrict chat access by blocking IPs. List IPs with commas.



Settings> Artificial Intelligence
OpenAI
OpenAI settings.

Sync mode

Manual
API key
••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••
Chatbot mode

Only questions related to your sources
Model

gpt-4o-mini
Fallback message
It seems I'm having trouble understanding. Can you give me a bit more information?
Prompt
 "Act as an AI-powered sales and support assistant for Masi Chat, an all-in-one customer communication platform designed to help small and medium businesses connect, support, and grow. Your primary goals are to:

Engage and educate potential customers about Masi Chat’s features, benefits, and how it improves customer communication.

Sell the platform by highlighting its value, answering objections, and encouraging businesses to sign up.

Convert inquiries into users by prompting them to open a free account with access to 100 messages to experience the software firsthand.

Provide support by assisting with basic troubleshooting and escalating complex issues to live agents when necessary.

You can begin a conversation with something like this, but you can play around with it -  Hi there! I’m Masi, your virtual assistant. I’m here to help you with any questions you have about Masi Chat. 

Recognize greeting customs in any language and use simple words when speaking in other non English languages.

Respond "I don't know", if you don't know or not sure about the answer.

Tone & Style: Be friendly, professional, and persuasive. Make responses clear, engaging, and results-driven. If a user expresses interest but hesitates, offer a compelling reason to try the free plan. If they are already users, help them maximize Masi Chat’s capabilities."
Prompt - Message rewriting
Max tokens
Temperature
Presence penalty
Frequency penalty
Logit bias
Custom model ID
Training data language

Default
Chatbot

Spelling correction

Smart reply

Dialogflow spelling correction

Message rewrite button

Generate user questions

Speech recognition

Source links

Note data scraping

Automatic training

Use conversations for training

Context awareness

Troubleshoot
OpenAI Assistants - Department linking
Set a dedicated OpenAI Assistants for each department.

Department ID
Assistant ID
Google
Google and Dialogflow settings.

Sync mode

Manual
Client ID
532127638027-fmuojlgrgv7ei7akq7qsgvmdh7o75kgf.apps.googleusercontent.com
Client secret
•••••••••••••••••••••••••••••••••••
Refresh token
1//03sjmwKYHhFkVCgYIARAAGAMSNwF-L9IrF1KIhKOPFB3rvvONH8RLjQtBT5y7-xtEB4a2yiAFAhOrUw5dk8Coz-k_sQIfzySiJfM
Project ID or Agent Name
Dialogflow location

Global
Dialogflow edition

Dialogflow ES
Dialogflow Intent detection confidence
Synchronize
Authorised redirect URI
Add Intents to saved replies
Dialogflow chatbot

Multilingual

Multilingual via translation

Automatic translation

Language detection

Language detection message
Dialogflow welcome Intent

Send user details

Troubleshoot
Dialogflow - Department linking
Set a dedicated Dialogflow agent for each department.

Department ID
Project ID
Google search
Let the chatbot search on Google to find answers to user questions.

Active

Spelling correction

Search engine ID
API key
Disable during office hours
Enable the chatbot outside of scheduled office hours only.


Disable for the tickets area
Disable the chatbot for the tickets area.


Human takeover
If the chatbot doesn't understand a user's question, forwards the conversation to an agent.

Active

Automatic human takeover

Disable chatbot

Message
It seems I’m not quite sure how to assist you with that. Would you like me to connect you with a live agent who can help you further? Just let me know!
Confirmation message
Got it! Your request has been received. We'll get back to you shortly, as soon as an agent is available. If you need further assistance, feel free to ask!
Fallback message
Hmm, I didn’t quite catch that. Could you rephrase your question? Or if you prefer, I can connect you with a live agent for more help!
Confirm button text
Cancel button text
Bot response delay
Add a delay (ms) to the bot's responses. Default is 2000.

Smart reply
Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.


Reply to user emails
Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.


Reply to user text messages
Allow the chatbot to reply to the user's text messages if the answer is known.


Usage Limit
Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.

Max message limit
Interval (sec)
Message



Settings=tickets
Panel height
Set the maximum height of the tickets panel.

Offset
Subtract the offset value from the height value.

Articles
Display the articles section in the right area.


Do not show tickets in chat
Hide tickets from the chat widget and chats from the ticket area.


Hide archived tickets
Hide archived tickets from users.


User name as title
Display the user full name in the left panel instead of the conversation title.


Default conversation name
Set the default name to use for conversations without a name.

Ticket field names
Change the default field names.

Title
Message
Panel name
Button name
Require registration
Require the user registration or login in order to use the tickets area.


Disable password
Disable the login and remove the password field from the registration form.


Default form
Set the default form to display if the registraion is required.


Login form
Ticket fields
Choose which fields to include in the new ticket form.

Departments

Priority

Ticket custom fields
Add custom fields to the new ticket form.

Name
Required

Disable features
Choose which fields to disable from the tickets area.

First ticket form

Edit profile

Left panel

Right panel

Panels arrows

Top bar

Left profile image

Department

New ticket button

Agent area

Agent details

Default department
Set the default departments for all tickets. Enter the department ID.

Welcome message
Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.

Active

Message
Hi  👋

Welcome to Masi Chat! Your support ticket has been created successfully. Our team is on it and will get back to you as soon as possible.

In the meantime, if you have any additional details to share, feel free to reply here. We're here to make sure you get the support you need!

Thank you for reaching out!

Best regards,
The Masi Chat Support Team


New conversation notification
Send an email to the user when a new conversation is created.


All channels
New conversation email
Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.

Subject
 Your Support Request Has Been Received!
Content
Hi {user_name},

Thank you for reaching out to us!

We've received your support request and created a new conversation with ID {conversation_id}. Our support team is on it and will get back to you as soon as possible.

You can view and manage your support conversation at any time using the link below:
{conversation_url_parameter}

Your Message:
"{message}"

If you attached any files, we’ve received them:
{attachments}

We’re here to help—feel free to reply to this email if you have any additional information to share.

Best regards,
The Masi Chat Support Team


Languages
Send message via enter button
Send the message via the ENTER keyboard button.


reCaptcha
Protect the tickets area from spam and abuse with Google reCAPTCHA.

Active

Site key
Secret key




Settings=messenger
Messenger and Instagram settings
Synchronize your Messenger and Instagram accounts.

Sync mode

Automatic
Synchronize now
Unsubscribe
Facebook pages
You will get this information by completing the synchronization.

Page name
Masi Chat
Page ID
***************
Page token
•••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••
Instagram ID
*****************
Department ID
Tags
Disable chatbot
Disable the chatbot for this channel only.



Settings=whatsapp
Cloud API settings
Synchronize your WhatsApp Cloud API account.

Sync mode

Automatic
Synchronize now
Reconnect
Cloud API numbers
Add WhatsApp phone number details here.

Label
Masi Chat Test
Phone number ID
***************
Business Account ID
***************
Token
•••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••
Department ID
Tags
Label
Phone number ID
***************
Business Account ID
***************
Token
•••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••••
Department ID
Tags
Cloud API template fallback
Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.

Template name
Template languages
Header variables
Body variables
Button variables
Test template
Send the message template to a WhatsApp number.

Text message fallback
Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.

Active

Message template
Order webhook
Send the WhatsApp order details to the URL provided.

Catalogue details
Your WhatsApp catalogue details.

Catalogue ID
Default header text
Default body text
Twilio settings
Enter your Twilio account settings information.

Account SID
Token
Sender
Configuration URL
Twilio template
Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.

Content template SID
Variables
360dialog settings
Enter your 360dialog account settings information.

API key
Synchronize
360dialog template
Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.

Namespace
Template name
Template default language
Custom parameters
Disable chatbot
Disable the chatbot for this channel only.


Clear flows
Delete the built-in flows.




Settings=twitter
Synchronization
Enter the details of your Twitter app.

Consumer key
Consumer secret
Access token
Access token secret
Your username
Dev environment label
Callback URL
Subscribe
Department ID
Assign a department to all conversations started from Twitter. Enter the department ID.

Disable chatbot
Disable the chatbot for this channel only.




Settings=apps [ai and tickets r internal others are integrations]

Artificial Intelligence
Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.


Tickets
Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.


Messenger
Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from Masi Chat.


WhatsApp
Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from Masi Chat.


Twitter
Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from Masi Chat.


Telegram
Connect your Telegram bot to Masi Chat to read and reply to all messages sent to your Telegram bot directly in Masi Chat.


Viber
Connect your Viber bot to Masi Chat to read and reply to all messages sent to your Viber bot directly in Masi Chat.


Line
Connect your LINE bot to Masi Chat to read and reply to all messages sent to your LINE bot directly in Masi Chat.


WeChat
Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from Masi Chat.


Zalo
Connect your Zalo Official Account to Masi Chat to read and reply to all messages sent to your Zalo Official Account directly in Masi Chat.


Slack
Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.


Zendesk
Automatically sync Zendesk customers with Masi Chat, view Zendesk tickets, or create new ones without leaving Masi Chat.



Settings
Display in dashboard
Show the articles panel on the chat dashboard.


Display categories
Show the categories instead of the articles list.


Panel title
Set the articles panel title. Default is 'Help Center'.

URL rewrite
Change the default URLs with user-friendly URLs.



Settings=articles
Display in dashboard
Show the articles panel on the chat dashboard.


Display categories
Show the categories instead of the articles list.


Panel title
Set the articles panel title. Default is 'Help Center'.

URL rewrite
Change the default URLs with user-friendly URLs.



Settings=automations
Messages
Name
Message
Conditions

Pop-ups
Name
Message
Title
Profile image
Message fallback

Conditions

Emails
Name
Message
Subject
Conditions

Design
Name
Message
Header title
Primary color
Secondary color
Tertiary color
Header background image
Header brand image
Chat button icon
Conditions

More
Name
Department ID
Agent ID
Tags
Article IDs
Articles category
Conditions

Question
Add question
Function calling
URL
Method

GET, POST, PUT, PATCH, and DELETE
Headers
Properties
Name
Description
Allowed values separated by commas

Set data and actions
([['name' => 'Name', 'id' => 'full_name'], ['name' => 'Email', 'id' => 'email'], ['name' => 'Password', 'id' => 'password']], sb_users_get_fields(), [['name' => 'Assign tags', 'id' => 'tags'], ['name' => 'Assign a department', 'id' => 'department'], ['name' => 'Assign an agent', 'id' => 'agent'], ['name' => 'Go to URL', 'id' => 'redirect'], ['name' => 'Show an article', 'id' => 'open_article'], ['name' => 'Download transcript', 'id' => 'transcript'], ['name' => 'Email transcript', 'id' => 'transcript_email'], ['name' => 'Send email to user', 'id' => 'send_email'], ['name' => 'Send email to agents', 'id' => 'send_email_agents'], ['name' => 'Archive the conversation', 'id' => 'archive_conversation'], ['name' => 'Human takeover', 'id' => 'human_takeover']]);
                                                                for ($i = 0; $i < count($fields); $i++) {
Enter the value

Name
Enter the value
